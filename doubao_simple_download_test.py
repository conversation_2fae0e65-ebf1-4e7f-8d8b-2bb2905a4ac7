#!/usr/bin/env python3
"""
豆包图片下载功能简化测试脚本
直接在每个容器中查找下载按钮并下载
"""

import asyncio
import os
import json
from datetime import datetime
from playwright.async_api import async_playwright

# 配置
OUTPUT_DIR = "test_images"
AUTH_STATE_FILE = "doubao_auth_state.json"
TEST_URL = "https://www.doubao.com/chat/7342260912932610"

async def main():
    print("🧪 豆包图片下载功能简化测试...")

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    async with async_playwright() as p:
        # 启动浏览器
        print("🚀 启动Chrome浏览器...")
        browser = await p.chromium.launch(
            channel='chrome',  # 使用系统安装的Chrome
            headless=False,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process'
            ]
        )

        # 创建上下文
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )

        # 加载登录状态
        if os.path.exists(AUTH_STATE_FILE):
            print(f"📂 加载保存的登录状态: {AUTH_STATE_FILE}")
            with open(AUTH_STATE_FILE, 'r') as f:
                auth_state = json.load(f)

            # 设置cookies
            if 'cookies' in auth_state:
                await context.add_cookies(auth_state['cookies'])
                print(f"✅ 成功加载 {len(auth_state['cookies'])} 个cookies")

        # 创建页面
        page = await context.new_page()

        try:
            # 访问测试页面
            print(f"🌐 访问测试页面: {TEST_URL}")
            response = await page.goto(TEST_URL, wait_until='networkidle', timeout=30000)
            print(f"✅ 页面加载成功，状态码: {response.status}")

            # 等待页面完全加载
            await asyncio.sleep(5)

            # 保存页面截图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            await page.screenshot(path=f"{OUTPUT_DIR}/test_page_{timestamp}.png")
            print(f"📸 已保存页面截图")

            # 查找所有容器
            print("🔍 查找页面中的所有容器...")
            container_count = await page.evaluate("""() => {
                const imageBoxes = document.querySelectorAll('[class*="image-box-grid-item"]');
                console.log(`找到 ${imageBoxes.length} 个图片容器`);
                return imageBoxes.length;
            }""")

            print(f"📊 找到 {container_count} 个容器")

            # 为每个容器测试下载功能
            for i in range(container_count):
                print(f"\n🖼️ 测试第 {i+1} 个容器下载...")

                try:
                    # 在特定容器中查找下载按钮
                    container_selector = f'[class*="image-box-grid-item"]:nth-of-type({i + 1})'
                    download_button = await page.query_selector(f'{container_selector} [data-testid="edit_image_hover_tag_download_btn"]')

                    if download_button:
                        print(f"✅ 在容器 {i+1} 中找到下载按钮！")

                        # 设置下载路径
                        download_path = os.path.join(os.path.abspath(OUTPUT_DIR), f"container_{i+1}_{timestamp}")

                        try:
                            # 方法1：尝试监听下载事件
                            print("🔄 尝试方法1：监听下载事件...")
                            download_success = False

                            try:
                                async with page.expect_download(timeout=3000) as download_info:
                                    await download_button.click()
                                    print("✅ 已点击下载按钮")

                                    download = await download_info.value
                                    print(f"📥 下载开始: {download.suggested_filename}")

                                    final_path = f"{download_path}_{download.suggested_filename}"
                                    await download.save_as(final_path)
                                    print(f"🎉 下载成功: {final_path}")
                                    download_success = True
                            except Exception as e:
                                print(f"⚠️ 方法1失败: {e}")

                            # 方法2：如果方法1失败，尝试直接获取图片并保存
                            if not download_success:
                                print("🔄 尝试方法2：直接获取图片数据...")
                                try:
                                    # 查找容器中的图片
                                    img_src = await page.evaluate(f"""() => {{
                                        const container = document.querySelector('[class*="image-box-grid-item"]:nth-of-type({i + 1})');
                                        if (!container) return null;

                                        // 查找各种可能的图片
                                        const img = container.querySelector('img[src*="ocean-cloud-tos"], img[src*="image_skill"]');
                                        if (img && img.src) return img.src;

                                        const source = container.querySelector('source[srcset*="ocean-cloud-tos"], source[srcset*="image_skill"]');
                                        if (source && source.srcset) return source.srcset.split(' ')[0];

                                        return null;
                                    }}""")

                                    if img_src:
                                        print(f"🔗 找到图片URL: {img_src[:100]}...")

                                        # 使用Playwright的request功能直接下载
                                        response = await page.request.get(img_src)
                                        if response.ok:
                                            image_data = await response.body()

                                            # 保存到指定路径
                                            final_path = f"{download_path}_method2.jpg"
                                            with open(final_path, 'wb') as f:
                                                f.write(image_data)

                                            print(f"🎉 方法2下载成功: {final_path} (大小: {len(image_data)} 字节)")
                                            download_success = True
                                        else:
                                            print(f"❌ 方法2失败: HTTP {response.status}")
                                    else:
                                        print("❌ 方法2失败: 未找到图片URL")

                                except Exception as e:
                                    print(f"❌ 方法2出错: {e}")

                            # 方法3：如果都失败，保存容器截图
                            if not download_success:
                                print("🔄 方法3：保存容器截图...")
                                container_element = await page.query_selector(container_selector)
                                if container_element:
                                    backup_path = f"{download_path}_screenshot.png"
                                    await container_element.screenshot(path=backup_path)
                                    print(f"💾 已保存容器截图: {backup_path}")

                        except Exception as e:
                            print(f"❌ 下载过程出错: {e}")
                    else:
                        print(f"❌ 容器 {i+1} 中未找到下载按钮")

                        # 保存容器截图用于调试
                        container_element = await page.query_selector(container_selector)
                        if container_element:
                            debug_path = f"{OUTPUT_DIR}/debug_container_{i+1}_{timestamp}.png"
                            await container_element.screenshot(path=debug_path)
                            print(f"🔍 已保存调试截图: {debug_path}")

                except Exception as e:
                    print(f"❌ 处理容器 {i+1} 时出错: {e}")

                # 短暂等待，避免操作过快
                await asyncio.sleep(1)

            print(f"\n🎉 测试完成！共测试了 {container_count} 个容器")

        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            await page.screenshot(path=f"{OUTPUT_DIR}/error_{timestamp}.png")

        finally:
            # 等待用户查看结果
            print("⏳ 等待5秒让用户查看结果...")
            await asyncio.sleep(5)

            # 关闭浏览器
            await browser.close()

if __name__ == "__main__":
    asyncio.run(main())
