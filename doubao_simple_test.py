#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import os
import time
import json
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, TimeoutError, Error

# 添加pyperclip库用于操作剪贴板
try:
    import pyperclip
    PYPERCLIP_AVAILABLE = True
except ImportError:
    PYPERCLIP_AVAILABLE = False
    print("注意: pyperclip库未安装，将使用备选方法。可以通过pip install pyperclip安装")

# 创建输出目录
OUTPUT_DIR = "test_images"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 保存登录状态的文件
AUTH_FILE = os.path.abspath("doubao_auth_state.json")
CONTEXT_DIR = os.path.abspath("browser_context")
os.makedirs(CONTEXT_DIR, exist_ok=True)

# 更长的超时时间
LONG_TIMEOUT = 120000  # 120秒
STANDARD_TIMEOUT = 60000  # 60秒
SHORT_TIMEOUT = 30000  # 30秒

# 最大重试次数
MAX_RETRIES = 3

async def wait_with_timeout(page, condition, timeout_ms, description):
    """带超时和重试的等待函数"""
    start_time = time.time()
    end_time = start_time + (timeout_ms / 1000)

    print(f"等待{description}...")

    for retry in range(MAX_RETRIES):
        try:
            result = await condition
            print(f"成功: {description}")
            return result
        except TimeoutError:
            current_time = time.time()
            if current_time >= end_time:
                print(f"超时: {description}，已重试{retry+1}次")
                if retry == MAX_RETRIES - 1:
                    # 最后一次重试，截图记录状态
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    error_path = os.path.join(OUTPUT_DIR, f"timeout_{description.replace(' ', '_')}_{timestamp}.png")
                    await page.screenshot(path=error_path)
                    print(f"已保存超时状态截图: {error_path}")
                    raise

                # 尝试刷新页面
                if retry > 0:
                    print(f"尝试刷新页面...")
                    await page.reload(timeout=STANDARD_TIMEOUT)
            else:
                # 还有时间，暂停后重试
                print(f"暂时未成功: {description}，等待5秒后重试...")
                await asyncio.sleep(5)
        except Error as e:
            print(f"操作出错: {description}, 错误: {e}")
            # 截图记录状态
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            error_path = os.path.join(OUTPUT_DIR, f"error_{description.replace(' ', '_')}_{timestamp}.png")
            await page.screenshot(path=error_path)
            print(f"已保存错误状态截图: {error_path}")
            raise

async def safe_click(page, element, description):
    """安全点击元素，包含重试逻辑"""
    for retry in range(MAX_RETRIES):
        try:
            await element.click(timeout=SHORT_TIMEOUT)
            print(f"成功点击: {description}")
            # 点击后等待页面稳定
            await asyncio.sleep(2)
            return True
        except Error as e:
            print(f"点击失败: {description}, 错误: {e}, 重试{retry+1}/{MAX_RETRIES}")
            if retry == MAX_RETRIES - 1:
                # 最后一次重试，截图记录状态
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                error_path = os.path.join(OUTPUT_DIR, f"click_failed_{description.replace(' ', '_')}_{timestamp}.png")
                await page.screenshot(path=error_path)
                print(f"已保存点击失败截图: {error_path}")
                return False
            await asyncio.sleep(2)  # 等待2秒后重试

async def main():
    print("测试豆包网站图片生成流程...")

    async with async_playwright() as p:
        browser = None
        try:
            # 尝试使用系统安装的Chrome
            print("启动Chrome浏览器...")
            browser = await p.chromium.launch(
                channel='chrome',  # 使用系统安装的Chrome
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=IsolateOrigins,site-per-process'
                ]
            )

            context = None
            new_session = False

            # 检查是否存在保存的登录状态
            auth_file_exists = os.path.isfile(AUTH_FILE)
            if auth_file_exists:
                print(f"找到保存的登录状态文件: {AUTH_FILE}")
                try:
                    # 读取状态文件内容并打印
                    with open(AUTH_FILE, 'r', encoding='utf-8') as f:
                        auth_content = json.load(f)
                        print(f"状态文件包含 {len(auth_content.get('cookies', []))} 个cookies")

                    # 创建新上下文并加载保存的状态
                    context = await browser.new_context(
                        storage_state=AUTH_FILE,
                        viewport={"width": 1280, "height": 800},
                        user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        ignore_https_errors=True
                    )
                    print("成功加载登录状态")
                except Exception as e:
                    print(f"加载登录状态失败: {e}，将创建新会话")
                    auth_file_exists = False
                    new_session = True

            if not auth_file_exists or new_session:
                # 创建新页面
                print("创建新页面...")
                context = await browser.new_context(
                    viewport={"width": 1280, "height": 800},
                    user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    ignore_https_errors=True
                )

            # 设置默认超时时间
            context.set_default_timeout(LONG_TIMEOUT)

            page = await context.new_page()

            try:
                # 访问豆包网站
                print("访问豆包网站...")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                # 第一次尝试访问
                for retry in range(MAX_RETRIES):
                    try:
                        response = await page.goto("https://www.doubao.com/chat/", timeout=LONG_TIMEOUT)
                        if response and response.status >= 200 and response.status < 400:
                            print(f"成功访问豆包网站，HTTP状态: {response.status}")
                            break
                        else:
                            status = response.status if response else "未知"
                            print(f"访问豆包网站返回错误状态: {status}，重试{retry+1}/{MAX_RETRIES}")
                            if retry < MAX_RETRIES - 1:
                                await asyncio.sleep(5)  # 等待5秒后重试
                    except Error as e:
                        print(f"访问豆包网站出错: {e}，重试{retry+1}/{MAX_RETRIES}")
                        if retry < MAX_RETRIES - 1:
                            await asyncio.sleep(5)  # 等待5秒后重试
                        else:
                            raise

                # 等待页面加载完成
                print("等待页面完全加载...")
                try:
                    # 尝试多种等待策略
                    await page.wait_for_load_state("domcontentloaded", timeout=SHORT_TIMEOUT)
                    print("DOM内容已加载")

                    await page.wait_for_load_state("load", timeout=STANDARD_TIMEOUT)
                    print("页面资源已加载")

                    # 等待网络活动停止
                    try:
                        await page.wait_for_load_state("networkidle", timeout=STANDARD_TIMEOUT)
                        print("网络活动已停止")
                    except TimeoutError:
                        print("等待网络活动停止超时，继续测试")
                except TimeoutError as e:
                    print(f"等待页面加载超时: {e}，继续测试")

                # 强制等待确保页面渲染
                print("额外等待5秒确保页面渲染...")
                await asyncio.sleep(5)

                # 截图页面
                loaded_path = os.path.join(OUTPUT_DIR, f"doubao_loaded_{timestamp}.png")
                await page.screenshot(path=loaded_path)
                print(f"已保存加载页面截图: {loaded_path}")

                # 检查是否需要登录
                print("检查是否需要登录...")
                login_found = False

                # 更全面的登录检测，使用普通函数而不是async lambda
                async def check_login_button():
                    try:
                        # 首先检查是否已经登录（积极检查）
                        logged_in_indicators = [
                            ".user-avatar",
                            ".avatar",
                            ".user-info",
                            ".user-profile",
                            ".avatar-img",
                            "[aria-label='用户设置']",
                            "[data-testid='avatar']"
                        ]

                        for indicator in logged_in_indicators:
                            if await page.query_selector(indicator):
                                print(f"检测到已登录指标: {indicator}")
                                return False  # 已登录

                        # 检查各种登录按钮（消极检查）
                        login_indicators = [
                            "text=登录",
                            "button:has-text('登录')",
                            "[aria-label='登录']",
                            "a:has-text('登录')",
                            ".login-required",
                            ".need-login"
                        ]

                        for indicator in login_indicators:
                            if await page.query_selector(indicator):
                                print(f"检测到登录指标: {indicator}")
                                return True  # 需要登录

                        # 如果没有明确迹象，假设已登录
                        print("未检测到任何登录或已登录指标，假设已登录")
                        return False
                    except Exception as e:
                        print(f"登录检测出错: {e}")
                        return False

                # 执行登录检查
                login_found = await check_login_button()

                if login_found:
                    print("检测到需要登录，请在浏览器中完成登录...")
                    print("等待30秒，请在此期间完成登录...")

                    # 保存登录前截图
                    login_before_path = os.path.join(OUTPUT_DIR, f"before_login_{timestamp}.png")
                    await page.screenshot(path=login_before_path)
                    print(f"已保存登录前截图: {login_before_path}")

                    # 等待用户手动登录
                    await asyncio.sleep(30)  # 从90秒改为30秒

                    # 登录后截图
                    login_path = os.path.join(OUTPUT_DIR, f"after_login_{timestamp}.png")
                    await page.screenshot(path=login_path)
                    print(f"已保存登录后截图: {login_path}")

                    # 登录成功后保存状态
                    print("正在保存登录状态...")

                    # 先保存浏览器状态
                    await context.storage_state(path=AUTH_FILE)

                    # 打印保存的状态信息
                    with open(AUTH_FILE, 'r', encoding='utf-8') as f:
                        auth_content = json.load(f)
                        print(f"保存了 {len(auth_content.get('cookies', []))} 个cookies")

                    print(f"登录状态已保存到: {AUTH_FILE}")

                    # 确认登录成功
                    print("检查登录是否成功...")
                    # 强制刷新页面
                    await page.reload()
                    await page.wait_for_load_state("networkidle", timeout=SHORT_TIMEOUT)

                    # 再次检查登录状态
                    login_needed = await check_login_button()

                    if login_needed:
                        print("登录可能未成功，请检查登录状态")
                    else:
                        print("登录确认成功")
                else:
                    print("未检测到登录按钮，可能已登录或页面结构发生变化")

                    # 如果使用的是之前保存的状态且没有检测到登录按钮，确认登录状态有效
                    if auth_file_exists:
                        print("已使用保存的登录状态，登录成功!")
                    else:
                        # 如果是新会话但没有检测到登录按钮，仍然保存状态
                        print("新会话无需登录，保存当前状态...")
                        await context.storage_state(path=AUTH_FILE)
                        print(f"状态已保存到: {AUTH_FILE}")

                # 查找图像生成入口
                print("查找图像生成入口...")
                image_selectors = [
                    "div[title='图像生成']",  # 根据用户提供的HTML添加精确选择器
                    ".section-item-title-BjpNe2",  # 使用class选择器
                    "div.title-IK319y[title='图像生成']",  # 组合选择器
                    "div:has-text('图像生成')",  # 文本选择器
                    "text=图像生成",
                    "text=图像",
                    "text=AI绘画",
                    "text=绘画",
                    "text=创作图像",
                    "[aria-label='图像生成']",
                    "button:has-text('绘画')",
                    "button:has-text('创作')",
                    ".sidebar-item:has-text('图像')",
                    "a:has-text('图像')"
                ]

                image_button = None
                for selector in image_selectors:
                    try:
                        # 使用较短超时快速检查多个选择器
                        image_button = await page.wait_for_selector(selector, timeout=5000, state="visible")
                        if image_button:
                            print(f"找到图像生成入口: {selector}")
                            break
                    except:
                        continue

                if not image_button:
                    print("未找到图像生成入口，尝试等待更长时间...")
                    # 再次尝试寻找，使用更长超时
                    for selector in image_selectors:
                        try:
                            image_button = await page.wait_for_selector(selector, timeout=20000, state="visible")
                            if image_button:
                                print(f"在延长等待后找到图像生成入口: {selector}")
                                break
                        except:
                            continue

                if image_button:
                    # 点击图像生成入口前截图
                    before_click_path = os.path.join(OUTPUT_DIR, f"before_click_image_{timestamp}.png")
                    await page.screenshot(path=before_click_path)
                    print(f"已保存点击前截图: {before_click_path}")

                    # 尝试使用JavaScript点击元素
                    try:
                        print("尝试使用JavaScript点击图像生成入口...")
                        # 获取元素的选择器
                        selector = await page.evaluate("""(element) => {
                            // 获取独特的选择器
                            if (element.id) {
                                return '#' + element.id;
                            }
                            if (element.title) {
                                return `div[title="${element.title}"]`;
                            }
                            if (element.className) {
                                return '.' + element.className.split(' ').join('.');
                            }
                            return '';
                        }""", image_button)

                        if selector:
                            # 使用JavaScript直接点击
                            await page.evaluate(f"""(selector) => {{
                                const element = document.querySelector(selector);
                                if (element) {{
                                    element.click();
                                    return true;
                                }}
                                return false;
                            }}""", selector)
                            print(f"使用JavaScript成功点击图像生成入口: {selector}")
                            click_success = True
                        else:
                            # 尝试常规点击
                            click_success = await safe_click(page, image_button, "图像生成入口")
                    except Exception as e:
                        print(f"JavaScript点击失败: {e}")
                        # 尝试常规点击作为备选方案
                        click_success = await safe_click(page, image_button, "图像生成入口")

                    if click_success:
                        # 等待页面变化
                        print("等待图像生成页面加载...")
                        try:
                            await page.wait_for_load_state("networkidle", timeout=SHORT_TIMEOUT)
                        except TimeoutError:
                            print("等待图像生成页面加载超时，继续测试")

                        # 强制等待确保页面渲染
                        print("额外等待5秒确保页面渲染...")
                        await asyncio.sleep(5)

                        # 截图图像生成页面
                        image_page_path = os.path.join(OUTPUT_DIR, f"image_page_{timestamp}.png")
                        await page.screenshot(path=image_page_path)
                        print(f"已保存图像生成页面截图: {image_page_path}")

                        # 查找输入框
                        print("查找输入框...")
                        input_selectors = [
                            "[data-slate-editor='true']",  # Slate编辑器选择器
                            "[data-testid='chat_input_input']",  # 聊天输入框测试ID
                            "[role='textbox']",
                            ".editor-hLysVw",
                            "div[contenteditable='true']",
                            "textarea",
                            "[contenteditable='true']",
                            ".prompt-box textarea",
                            ".input-area textarea",
                            "textarea[placeholder]"
                        ]

                        input_box = None
                        for selector in input_selectors:
                            try:
                                input_box = await page.query_selector(selector)
                                if input_box:
                                    print(f"找到输入框: {selector}")
                                    break
                            except:
                                continue

                        if input_box:
                            print("找到输入框，输入测试提示词...")
                            test_prompt = "童书插画，水彩画风格：阳光下的彩虹森林全景，树冠漏下金色光斑，高质量儿童读物插图，柔和色调，温暖光感"

                            # 使用复制粘贴方法输入提示词
                            try:
                                print("使用复制粘贴方法输入...")

                                # 方法1: 使用pyperclip库复制到系统剪贴板
                                original_clipboard = None
                                if PYPERCLIP_AVAILABLE:
                                    try:
                                        # 保存原始剪贴板内容
                                        original_clipboard = pyperclip.paste()
                                        # 设置新的剪贴板内容
                                        pyperclip.copy(test_prompt)
                                        print("已复制提示词到系统剪贴板")
                                    except Exception as e:
                                        print(f"使用pyperclip复制失败: {e}")

                                # 清空编辑器内容
                                await page.evaluate("""() => {
                                    const editors = document.querySelectorAll('[data-slate-editor="true"]');
                                    for (let editor of editors) {
                                        editor.innerHTML = '';
                                        console.log("已清空编辑器内容");
                                    }
                                }""")

                                # 点击以聚焦输入框
                                await input_box.click()
                                await asyncio.sleep(1)

                                # 方法2: 使用Playwright的键盘快捷键模拟粘贴操作
                                print("尝试使用键盘快捷键粘贴...")

                                # 检测操作系统类型，使用相应的快捷键
                                import platform
                                if platform.system() == 'Darwin':  # macOS
                                    # 使用Command+V
                                    await page.keyboard.press("Meta+V")
                                else:
                                    # 使用Ctrl+V
                                    await page.keyboard.press("Control+V")

                                await asyncio.sleep(2)

                                # 检查粘贴是否成功
                                text_content = await page.evaluate("""() => {
                                    const spans = document.querySelectorAll('span[data-slate-string="true"]');
                                    if (spans && spans.length > 0) {
                                        return spans[0].textContent || '';
                                    }
                                    return '';
                                }""")

                                print(f"粘贴后文本内容: {text_content[:20]}...")

                                # 如果粘贴不成功，尝试方法3: 使用JavaScript的execCommand
                                if not text_content:
                                    print("键盘快捷键粘贴失败，尝试使用execCommand方法...")

                                    # 在页面中创建一个隐藏的textarea元素
                                    paste_result = await page.evaluate("""(text) => {
                                        // 创建一个临时textarea存放文本
                                        const textarea = document.createElement('textarea');
                                        textarea.value = text;
                                        document.body.appendChild(textarea);
                                        textarea.select();

                                        // 尝试使用document.execCommand执行粘贴
                                        let success = false;
                                        try {
                                            // 使用execCommand复制文本到剪贴板
                                            if (document.execCommand('copy')) {
                                                console.log("成功复制到剪贴板");

                                                // 聚焦到Slate编辑器
                                                const editor = document.querySelector('[data-slate-editor="true"]');
                                                if (editor) {
                                                    editor.focus();
                                                    // 尝试粘贴
                                                    if (document.execCommand('paste')) {
                                                        console.log("execCommand粘贴成功");
                                                        success = true;
                                                    }
                                                }
                                            }
                                        } catch (e) {
                                            console.error("execCommand粘贴失败:", e);
                                        }

                                        // 移除临时元素
                                        document.body.removeChild(textarea);

                                        // 如果粘贴失败，直接设置内容
                                        if (!success) {
                                            try {
                                                const spans = document.querySelectorAll('span[data-slate-string="true"]');
                                                if (spans && spans.length > 0) {
                                                    spans[0].textContent = text;
                                                    console.log("直接设置文本内容");

                                                    // 触发必要的事件
                                                    const editor = document.querySelector('[data-slate-editor="true"]');
                                                    if (editor) {
                                                        ['input', 'change'].forEach(event =>
                                                            editor.dispatchEvent(new Event(event, {bubbles: true}))
                                                        );
                                                    }
                                                    success = true;
                                                }
                                            } catch (e) {
                                                console.error("直接设置文本失败:", e);
                                            }
                                        }

                                        return { success, method: success ? (success === true ? "execCommand或直接设置" : "键盘快捷键") : "全部失败" };
                                    }""", test_prompt)

                                    print(f"execCommand粘贴结果: {paste_result}")

                                # 截图记录输入状态
                                input_path = os.path.join(OUTPUT_DIR, f"input_complete_{timestamp}.png")
                                await page.screenshot(path=input_path)
                                print(f"已保存输入完成截图: {input_path}")

                                # 尝试激活发送按钮
                                print("尝试激活发送按钮...")
                                button_result = await page.evaluate("""() => {
                                    const button = document.querySelector('#flow-end-msg-send');
                                    if (!button) return { success: false, error: "未找到按钮" };

                                    try {
                                        // 检查按钮状态
                                        const beforeState = {
                                            disabled: button.disabled,
                                            ariaDisabled: button.getAttribute('aria-disabled'),
                                            classList: button.className
                                        };

                                        // 移除禁用状态
                                        button.disabled = false;
                                        button.removeAttribute('disabled');
                                        button.removeAttribute('aria-disabled');

                                        // 修改CSS类
                                        button.className = button.className
                                            .replace('semi-button-disabled', 'semi-button')
                                            .replace('semi-button-primary-disabled', 'semi-button-primary');

                                        // 尝试点击
                                        button.click();

                                        // 触发点击事件
                                        const clickEvent = new MouseEvent('click', {
                                            bubbles: true,
                                            cancelable: true,
                                            view: window
                                        });
                                        button.dispatchEvent(clickEvent);

                                        // 检查修改后状态
                                        const afterState = {
                                            disabled: button.disabled,
                                            ariaDisabled: button.getAttribute('aria-disabled'),
                                            classList: button.className
                                        };

                                        return {
                                            success: true,
                                            beforeState,
                                            afterState
                                        };
                                    } catch (e) {
                                        return { success: false, error: e.toString() };
                                    }
                                }""")

                                print(f"按钮激活结果: {button_result}")

                                # 额外等待以检查是否有延迟效果
                                await asyncio.sleep(5)

                                # 再次截图
                                after_path = os.path.join(OUTPUT_DIR, f"after_button_click_{timestamp}.png")
                                await page.screenshot(path=after_path)
                                print(f"已保存按钮点击后截图: {after_path}")

                                # 等待图像生成完成
                                print("等待图像生成过程...")

                                # 增加等待时间，确保有足够时间完成生成
                                max_wait_time = 180  # 最长等待3分钟
                                check_interval = 5   # 每5秒检查一次

                                # 监听下载事件
                                page.on("download", lambda download: print(f"检测到下载事件: {download.suggested_filename}"))

                                # 分阶段检测：先等待图像生成，再查找下载按钮
                                image_generated = False
                                generated_image_info = None

                                for i in range(max_wait_time // check_interval):
                                    print(f"等待图片生成中... ({i+1}/{max_wait_time // check_interval})")

                                    # 截图当前状态
                                    if i % 3 == 0:  # 每15秒保存一次截图
                                        waiting_path = os.path.join(OUTPUT_DIR, f"waiting_for_image_{i}_{timestamp}.png")
                                        await page.screenshot(path=waiting_path)
                                        print(f"已保存等待状态截图: {waiting_path}")

                                    # 第一阶段：专注检测图像生成完成
                                    if not image_generated:
                                        image_check_result = await page.evaluate("""() => {
                                            // 优先检查真实的生成图像
                                            const imageSelectors = [
                                                "img[src*='doubao']",
                                                "img[src*='generated']",
                                                "img[src*='create']",
                                                ".result-image img",
                                                ".image-container img",
                                                ".generated-image img",
                                                "img[alt*='生成']",
                                                "img[alt*='Generated']",
                                                "canvas[data-generated='true']"
                                            ];

                                            // 查找真实的生成图像
                                            for (const selector of imageSelectors) {
                                                const elements = document.querySelectorAll(selector);
                                                if (elements && elements.length > 0) {
                                                    for (const img of elements) {
                                                        if (img.complete && img.naturalWidth > 0 && img.naturalHeight > 0) {
                                                            const src = img.src || '';
                                                            const width = img.width || img.naturalWidth;
                                                            const height = img.height || img.naturalHeight;

                                                            // 排除小图标和非生成图像
                                                            if (src.includes('intro') ||
                                                                src.includes('logo') ||
                                                                src.includes('icon') ||
                                                                src.includes('avatar') ||
                                                                width < 200 ||
                                                                height < 200) {
                                                                console.log("跳过非生成图像:", src, width + "x" + height);
                                                                continue;
                                                            }

                                                            console.log("找到生成图像:", src, width + "x" + height);
                                                            return {
                                                                found: true,
                                                                type: "generated_image",
                                                                selector: selector,
                                                                src: src,
                                                                width: width,
                                                                height: height
                                                            };
                                                        }
                                                    }
                                                }
                                            }

                                            // 检查是否有生成进度指示器
                                            const progressIndicators = [
                                                ".generating",
                                                ".progress",
                                                ".loading",
                                                "[data-status='generating']",
                                                ".generation-progress"
                                            ];

                                            for (const indicator of progressIndicators) {
                                                if (document.querySelector(indicator)) {
                                                    console.log("检测到生成进度指示器:", indicator);
                                                    return { found: false, status: "generating" };
                                                }
                                            }

                                            return { found: false, status: "waiting" };
                                        }""")

                                        print(f"图像生成检查结果: {image_check_result}")

                                        # 如果找到生成的图像
                                        if image_check_result and image_check_result.get("found", False):
                                            if image_check_result.get("type") == "generated_image":
                                                print(f"✅ 检测到图像生成完成: {image_check_result}")
                                                image_generated = True
                                                generated_image_info = image_check_result

                                                # 保存生成结果截图
                                                result_path = os.path.join(OUTPUT_DIR, f"generation_result_{timestamp}.png")
                                                await page.screenshot(path=result_path)
                                                print(f"已保存生成结果截图: {result_path}")

                                                # 尝试直接截图保存生成的图像
                                                try:
                                                    image_selector = image_check_result.get("selector")
                                                    image_element = await page.query_selector(image_selector)
                                                    if image_element:
                                                        image_path = os.path.join(OUTPUT_DIR, f"generated_image_{timestamp}.png")
                                                        await image_element.screenshot(path=image_path)
                                                        print(f"已直接保存生成图像: {image_path}")
                                                except Error as e:
                                                    print(f"保存图像截图失败: {e}")

                                                # 继续到第二阶段：查找下载按钮
                                                continue

                                    # 第二阶段：图像生成完成后，查找对应的下载按钮
                                    if image_generated and generated_image_info:
                                        print("🔍 开始查找图片下载按钮...")

                                        # 查找与生成图像相关的下载按钮
                                        download_button_result = await page.evaluate("""(imageInfo) => {
                                            // 首先尝试找到图像元素
                                            const imageElement = document.querySelector(imageInfo.selector);
                                            if (!imageElement) {
                                                return { found: false, error: "图像元素未找到" };
                                            }

                                            // 查找图像元素附近的下载按钮
                                            let container = imageElement.closest('.message-content, .result-container, .image-result, .generation-result');
                                            if (!container) {
                                                // 如果没有找到容器，使用父元素
                                                container = imageElement.parentElement;
                                                let attempts = 0;
                                                while (container && attempts < 5) {
                                                    const downloadBtn = container.querySelector('button[class*="download"], a[class*="download"], [aria-label*="下载"]');
                                                    if (downloadBtn) {
                                                        container = downloadBtn.closest('div');
                                                        break;
                                                    }
                                                    container = container.parentElement;
                                                    attempts++;
                                                }
                                            }

                                            if (!container) {
                                                return { found: false, error: "未找到图像容器" };
                                            }

                                            // 在容器内查找下载按钮，优先级排序
                                            const downloadSelectors = [
                                                'button[aria-label*="下载图片"]',
                                                'button[aria-label*="下载图像"]',
                                                'button[title*="下载图片"]',
                                                'button[title*="下载图像"]',
                                                'a[aria-label*="下载图片"]',
                                                'a[title*="下载图片"]',
                                                'button[class*="download"]:not([class*="app"]):not([class*="client"])',
                                                'a[class*="download"]:not([class*="app"]):not([class*="client"])',
                                                'button[data-action="download"]',
                                                'a[data-action="download"]'
                                            ];

                                            for (const selector of downloadSelectors) {
                                                const btn = container.querySelector(selector);
                                                if (btn) {
                                                    // 验证这不是应用下载按钮
                                                    const btnText = btn.textContent || btn.getAttribute('aria-label') || btn.getAttribute('title') || '';
                                                    if (btnText.includes('应用') || btnText.includes('客户端') || btnText.includes('APP') || btnText.includes('安装')) {
                                                        console.log("跳过应用下载按钮:", btnText);
                                                        continue;
                                                    }

                                                    return {
                                                        found: true,
                                                        selector: selector,
                                                        element: btn,
                                                        text: btnText,
                                                        type: "image_download"
                                                    };
                                                }
                                            }

                                            // 如果精确选择器没找到，尝试文本匹配
                                            const allButtons = container.querySelectorAll('button, a');
                                            for (const btn of allButtons) {
                                                const text = btn.textContent || '';
                                                const ariaLabel = btn.getAttribute('aria-label') || '';
                                                const title = btn.getAttribute('title') || '';
                                                const fullText = (text + ' ' + ariaLabel + ' ' title).toLowerCase();

                                                // 排除应用下载相关的按钮
                                                if (fullText.includes('应用') || fullText.includes('客户端') ||
                                                    fullText.includes('app') || fullText.includes('安装') ||
                                                    fullText.includes('install') || fullText.includes('client')) {
                                                    continue;
                                                }

                                                // 查找图片下载相关的按钮
                                                if (fullText.includes('下载') &&
                                                    (fullText.includes('图') || fullText.includes('image') || fullText.includes('保存'))) {
                                                    return {
                                                        found: true,
                                                        element: btn,
                                                        text: text.trim(),
                                                        type: "image_download_text"
                                                    };
                                                }
                                            }

                                            return { found: false, error: "未找到图片下载按钮" };
                                        }""", generated_image_info)

                                        print(f"下载按钮查找结果: {download_button_result}")

                                        if download_button_result and download_button_result.get("found", False):
                                            print(f"✅ 找到图片下载按钮: {download_button_result}")

                                            # 设置下载路径
                                            download_path = os.path.join(os.path.abspath(OUTPUT_DIR), f"doubao_image_{timestamp}.png")

                                            # 点击下载按钮
                                            print("🔽 开始下载图片...")
                                            try:
                                                # 根据查找结果确定选择器
                                                if download_button_result.get("selector"):
                                                    download_selector = download_button_result["selector"]
                                                else:
                                                    # 使用JavaScript直接点击找到的元素
                                                    download_success = await page.evaluate("""(buttonInfo) => {
                                                        // 重新查找按钮元素
                                                        const imageElement = document.querySelector(buttonInfo.imageSelector);
                                                        if (!imageElement) return false;

                                                        let container = imageElement.closest('.message-content, .result-container, .image-result, .generation-result');
                                                        if (!container) container = imageElement.parentElement;

                                                        const buttons = container.querySelectorAll('button, a');
                                                        for (const btn of buttons) {
                                                            const text = btn.textContent || '';
                                                            if (text.includes('下载') && !text.includes('应用') && !text.includes('客户端')) {
                                                                btn.click();
                                                                return true;
                                                            }
                                                        }
                                                        return false;
                                                    }""", {"imageSelector": generated_image_info["selector"]})

                                                    if download_success:
                                                        print("✅ 使用JavaScript成功点击下载按钮")
                                                    else:
                                                        print("❌ JavaScript点击失败")
                                                        continue

                                                # 如果有选择器，使用Playwright API
                                                if download_button_result.get("selector"):
                                                    async with page.expect_download(timeout=30000) as download_info:
                                                        download_button = await page.query_selector(download_selector)
                                                        if download_button:
                                                            await download_button.click()
                                                            print("✅ 已点击下载按钮")

                                                        # 等待下载开始
                                                        download = await download_info.value
                                                        print(f"📥 检测到下载: {download.suggested_filename}")

                                                        # 验证下载的文件类型
                                                        filename = download.suggested_filename
                                                        if filename and (filename.endswith('.png') or filename.endswith('.jpg') or
                                                                        filename.endswith('.jpeg') or filename.endswith('.webp')):
                                                            # 保存文件
                                                            await download.save_as(download_path)
                                                            print(f"🎉 成功保存图片文件: {download_path}")
                                                            break  # 成功下载，跳出循环
                                                        else:
                                                            print(f"⚠️ 下载的文件不是图片: {filename}")
                                                            # 继续查找其他下载按钮
                                                            continue

                                            except Error as e:
                                                print(f"❌ 下载操作失败: {e}")

                                                # 备选方案：保存页面截图
                                                try:
                                                    print("💾 下载失败，保存页面截图作为备选...")
                                                    backup_path = os.path.join(OUTPUT_DIR, f"download_failed_backup_{timestamp}.png")
                                                    await page.screenshot(path=backup_path)
                                                    print(f"已保存备选截图: {backup_path}")
                                                except Exception as e2:
                                                    print(f"保存备选截图也失败: {e2}")
                                        else:
                                            print("❌ 未找到图片下载按钮")

                                            # 如果没找到下载按钮，尝试右键保存图片
                                            print("🖱️ 尝试右键保存图片...")
                                            try:
                                                image_element = await page.query_selector(generated_image_info["selector"])
                                                if image_element:
                                                    # 直接截图保存图片元素
                                                    image_path = os.path.join(OUTPUT_DIR, f"right_click_saved_image_{timestamp}.png")
                                                    await image_element.screenshot(path=image_path)
                                                    print(f"✅ 已通过截图保存图片: {image_path}")
                                            except Exception as e:
                                                print(f"右键保存失败: {e}")

                                        # 找到图像和处理下载后跳出循环
                                        break

                                    # 等待一段时间后再次检查
                                    await asyncio.sleep(check_interval)

                                # 最终状态截图
                                result_path = os.path.join(OUTPUT_DIR, f"final_state_{timestamp}.png")
                                await page.screenshot(path=result_path)
                                print(f"已保存最终状态截图: {result_path}")

                                # 恢复原始剪贴板内容
                                if PYPERCLIP_AVAILABLE and original_clipboard is not None:
                                    try:
                                        pyperclip.copy(original_clipboard)
                                        print("已恢复原始剪贴板内容")
                                    except:
                                        pass

                            except Error as e:
                                print(f"复制粘贴方法失败: {e}")
                                # 截图错误状态
                                error_path = os.path.join(OUTPUT_DIR, f"input_error_{timestamp}.png")
                                await page.screenshot(path=error_path)
                                print(f"已保存错误状态截图: {error_path}")
                        else:
                            print("未找到输入框")
                    else:
                        print("点击图像生成入口失败")
                else:
                    print("未找到图像生成入口")

                # 最后等待一段时间让用户查看
                print("测试完成，等待30秒让用户查看...")
                await asyncio.sleep(30)

            except Exception as e:
                print(f"测试过程中出错: {e}")
                try:
                    # 截图错误状态
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    error_path = os.path.join(OUTPUT_DIR, f"error_{timestamp}.png")
                    await page.screenshot(path=error_path)
                    print(f"已保存错误状态截图: {error_path}")
                except:
                    pass

            # 关闭浏览器
            print("关闭浏览器...")
            if browser:
                await browser.close()

        except Exception as e:
            print(f"浏览器启动过程中出错: {e}")
            if browser:
                await browser.close()

if __name__ == "__main__":
    asyncio.run(main())