现在下载这一块还是存在问题，我们先编写一个简单的测试脚本来调试下载这一块

可以加载原来的登录信息后，打开 https://www.doubao.com/chat/7342260912932610 这个页面，测试下载原图的功能，我会在下方提供一些线索，请你根据线索开始测试

【待下载图片】的html格式
<source type="image/avif" srcset="https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/308a6a01-02f9-4100-9be2-4459449ccb83_1748505896662776425_origin~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=b14c611d&amp;x-expires=1780041896&amp;x-signature=KfG00AJiYeNpj0J0vDFg2yV2M6w= 1x, https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/308a6a01-02f9-4100-9be2-4459449ccb83_1748505896662776425_origin~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=b14c611d&amp;x-expires=1780041896&amp;x-signature=KfG00AJiYeNpj0J0vDFg2yV2M6w= 2x" src="https://p9-flow-imagex-sign.byteimg.com/ocean-cloud-tos/image_skill/308a6a01-02f9-4100-9be2-4459449ccb83_1748505896662776425_origin~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=b14c611d&amp;x-expires=1780041896&amp;x-signature=KfG00AJiYeNpj0J0vDFg2yV2M6w=">

【下载原图】按钮的html格式
<div data-testid="edit_image_hover_tag_download_btn" class="hover-UadIFh"><div class="flex items-center" tabindex="0" aria-describedby="u0nmqcb" data-popupid="u0nmqcb"><span role="img" class="semi-icon semi-icon-default p-4 text-14"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M20.207 12.207a1 1 0 0 0-1.414-1.414L13 16.586V2a1 1 0 1 0-2 0v14.586l-5.793-5.793a1 1 0 0 0-1.414 1.414l7.5 7.5c.195.195.45.293.706.293H5a1 1 0 1 0 0 2h14a1 1 0 1 0 0-2h-6.999a1 1 0 0 0 .706-.293z"></path></svg></span></div></div>
