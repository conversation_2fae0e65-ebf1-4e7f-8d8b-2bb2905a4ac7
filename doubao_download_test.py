#!/usr/bin/env python3
"""
豆包图片下载功能专项测试脚本
基于提供的HTML结构信息进行精确测试
"""

import asyncio
import os
import json
from datetime import datetime
from playwright.async_api import async_playwright

# 配置
OUTPUT_DIR = "test_images"
AUTH_STATE_FILE = "doubao_auth_state.json"
TEST_URL = "https://www.doubao.com/chat/7259771592165890"

async def main():
    print("🧪 豆包图片下载功能专项测试...")

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    async with async_playwright() as p:
        # 启动浏览器
        print("🚀 启动Chrome浏览器...")
        browser = await p.chromium.launch(
            channel='chrome',  # 使用系统安装的Chrome
            headless=False,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process'
            ]
        )

        # 创建上下文
        context = await browser.new_context(
            # viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )

        # 加载登录状态
        if os.path.exists(AUTH_STATE_FILE):
            print(f"📂 加载保存的登录状态: {AUTH_STATE_FILE}")
            with open(AUTH_STATE_FILE, 'r') as f:
                auth_state = json.load(f)

            # 设置cookies
            if 'cookies' in auth_state:
                await context.add_cookies(auth_state['cookies'])
                print(f"✅ 成功加载 {len(auth_state['cookies'])} 个cookies")

        # 创建页面
        page = await context.new_page()

        # 设置下载路径
        await page.set_extra_http_headers({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        })

        try:
            # 访问测试页面
            print(f"🌐 访问测试页面: {TEST_URL}")
            response = await page.goto(TEST_URL, wait_until='networkidle', timeout=30000)
            print(f"✅ 页面加载成功，状态码: {response.status}")

            # 等待页面完全加载
            await asyncio.sleep(10)

            # 保存页面截图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            await page.screenshot(path=f"{OUTPUT_DIR}/test_page_{timestamp}.png")
            print(f"📸 已保存页面截图")

            # 查找所有图片
            print("🔍 查找页面中的所有图片...")
            images_info = await page.evaluate("""() => {
                const images = [];

                // 根据关键线索：查找class包含"image-box-grid-item"的区域
                const imageBoxes = document.querySelectorAll('[class*="image-box-grid-item"]');
                console.log(`找到 ${imageBoxes.length} 个图片容器`);

                // 只处理包含图片的容器，并重新编号
                let validBoxIndex = 0;
                imageBoxes.forEach((box, originalBoxIndex) => {
                    // 在每个容器中查找图片
                    const img = box.querySelector('img[src*="image_skill"]');
                    if (img && img.src) {
                        images.push({
                            type: 'img_in_box',
                            boxIndex: validBoxIndex,  // 使用重新编号的索引
                            originalBoxIndex: originalBoxIndex,  // 保留原始索引
                            src: img.src,
                            width: img.width || img.naturalWidth,
                            height: img.height || img.naturalHeight,
                            container: box,
                            img: img
                        });
                        validBoxIndex++;
                    } else {
                        // 如果没有找到img，查找source标签
                        const source = box.querySelector('source[type="image/avif"][srcset*="image_skill"]');
                        if (source) {
                            const srcset = source.getAttribute('srcset');
                            const src = srcset.split(' ')[0]; // 取第一个URL
                            images.push({
                                type: 'source_in_box',
                                boxIndex: validBoxIndex,  // 使用重新编号的索引
                                originalBoxIndex: originalBoxIndex,  // 保留原始索引
                                srcset: srcset,
                                src: src,
                                container: box,
                                source: source
                            });
                            validBoxIndex++;
                        }
                    }
                });

                console.log(`在 ${imageBoxes.length} 个容器中找到 ${images.length} 张图片`);
                return images;
            }""")

            print(f"📊 找到 {len(images_info)} 张图片")

            # 为每张图片测试下载功能
            for i, image_info in enumerate(images_info):
                print(f"\n🖼️ 测试第 {i+1} 张图片下载...")
                print(f"   类型: {image_info['type']}")
                print(f"   容器索引: {image_info['boxIndex']}")
                print(f"   源: {image_info['src'][:100]}...")

                try:
                    # 首先清除之前的悬停状态
                    print("🧹 清除之前的悬停状态...")
                    await page.mouse.move(0, 0)
                    await asyncio.sleep(2)

                    # 确保所有下载按钮都隐藏
                    await page.evaluate("""() => {
                        const buttons = document.querySelectorAll('[data-testid="edit_image_hover_tag_download_btn"]');
                        buttons.forEach(btn => {
                            if (btn.style) btn.style.display = 'none';
                        });
                    }""")
                    await asyncio.sleep(1)

                    # 根据原始容器索引找到对应的图片元素
                    img_selector = f'[class*="image-box-grid-item"]:nth-of-type({image_info["originalBoxIndex"] + 1}) img[src*="image_skill"]'
                    img_element = await page.query_selector(img_selector)

                    if not img_element:
                        # 备选方案：通过src直接查找
                        img_element = await page.query_selector(f'img[src="{image_info["src"]}"]')

                    if not img_element:
                        print("❌ 未找到图片元素")
                        continue

                    print("✅ 找到图片元素，开始悬停...")

                    # 鼠标悬停到图片上
                    await img_element.hover()
                    await asyncio.sleep(3)  # 增加等待时间，确保悬停效果完全出现

                    # 查找下载按钮（在对应的容器中）
                    print("🔍 查找下载按钮...")

                    # 在特定容器内查找下载按钮
                    container_selector = f'[class*="image-box-grid-item"]:nth-of-type({image_info["originalBoxIndex"] + 1})'
                    download_button = await page.query_selector(f'{container_selector} [data-testid="edit_image_hover_tag_download_btn"]')

                    if not download_button:
                        # 备选方案：查找整个页面中可见的下载按钮
                        download_button = await page.query_selector('[data-testid="edit_image_hover_tag_download_btn"]:visible')

                    if not download_button:
                        # 最后备选：查找任何下载按钮
                        download_button = await page.query_selector('[data-testid="edit_image_hover_tag_download_btn"]')

                    if download_button:
                        # 获取下载按钮的详细信息用于调试
                        button_info = await page.evaluate("""(selector) => {
                            const button = document.querySelector(selector);
                            if (!button) return null;

                            const container = button.closest('[class*="image-box-grid-item"]');
                            const containerIndex = container ? Array.from(document.querySelectorAll('[class*="image-box-grid-item"]')).indexOf(container) : -1;

                            return {
                                containerIndex: containerIndex,
                                visible: button.offsetParent !== null,
                                boundingRect: button.getBoundingClientRect()
                            };
                        }""", f'[data-testid="edit_image_hover_tag_download_btn"]')

                        print(f"✅ 找到下载按钮！容器索引: {button_info.get('containerIndex', '未知') if button_info else '未知'}")
                        print(f"   期望容器索引: {image_info['boxIndex']}")

                        # 验证是否是正确容器的按钮
                        if button_info and button_info.get('containerIndex') != image_info['boxIndex']:
                            print(f"⚠️ 警告：找到的按钮不在期望的容器中！")
                            # 尝试重新悬停当前图片
                            await img_element.hover()
                            await asyncio.sleep(3)
                            # 重新查找按钮
                            new_download_button = await page.query_selector(f'{container_selector} [data-testid="edit_image_hover_tag_download_btn"]')
                            if new_download_button:
                                download_button = new_download_button
                                print("✅ 重新找到正确容器的下载按钮")
                            else:
                                print("⚠️ 重新查找失败，使用原按钮")

                        # 设置下载监听
                        download_path = os.path.join(os.path.abspath(OUTPUT_DIR), f"downloaded_image_{i+1}_{timestamp}")

                        try:
                            # 方法1：尝试监听下载事件
                            print("🔄 尝试方法1：监听下载事件...")
                            download_success = False

                            try:
                                async with page.expect_download(timeout=5000) as download_info:
                                    await download_button.click()
                                    print("✅ 已点击下载按钮")

                                    download = await download_info.value
                                    print(f"📥 下载开始: {download.suggested_filename}")

                                    final_path = f"{download_path}_{download.suggested_filename}"
                                    await download.save_as(final_path)
                                    print(f"🎉 下载成功: {final_path}")
                                    download_success = True
                            except Exception as e:
                                print(f"⚠️ 方法1失败: {e}")

                            # 方法2：如果下载事件失败，尝试直接获取图片并保存
                            if not download_success:
                                print("🔄 尝试方法2：直接获取图片数据...")
                                try:
                                    # 获取原始图片URL
                                    original_url = await page.evaluate("""(src) => {
                                        // 尝试从src中提取原始URL
                                        if (src.includes('~tplv-')) {
                                            // 移除缩略图参数，获取原图
                                            return src.split('~tplv-')[0] + '_origin';
                                        }
                                        return src;
                                    }""", image_info['src'])

                                    print(f"🔗 原始图片URL: {original_url}")

                                    # 使用Playwright的request功能直接下载
                                    response = await page.request.get(original_url)
                                    if response.ok:
                                        image_data = await response.body()

                                        # 保存到指定路径
                                        final_path = f"{download_path}_method2.jpg"
                                        with open(final_path, 'wb') as f:
                                            f.write(image_data)

                                        print(f"🎉 方法2下载成功: {final_path} (大小: {len(image_data)} 字节)")
                                        download_success = True
                                    else:
                                        print(f"❌ 方法2失败: HTTP {response.status}")

                                except Exception as e:
                                    print(f"❌ 方法2出错: {e}")

                            # 方法3：如果方法2也失败，尝试重新点击下载按钮并等待更长时间
                            if not download_success:
                                print("🔄 尝试方法3：重新点击下载按钮...")
                                try:
                                    async with page.expect_download(timeout=10000) as download_info:
                                        await download_button.click()
                                        print("✅ 重新点击下载按钮")

                                        download = await download_info.value
                                        print(f"📥 检测到下载: {download.suggested_filename}")

                                        final_path = f"{download_path}_method3_{download.suggested_filename}"
                                        await download.save_as(final_path)
                                        print(f"🎉 方法3下载成功: {final_path}")
                                        download_success = True

                                except Exception as e:
                                    print(f"❌ 方法3出错: {e}")

                            # 方法4：如果都失败，保存截图
                            if not download_success:
                                print("🔄 方法4：保存截图备份...")
                                backup_path = f"{download_path}_screenshot.png"
                                await img_element.screenshot(path=backup_path)
                                print(f"💾 已保存截图备份: {backup_path}")

                        except Exception as e:
                            print(f"❌ 下载过程出错: {e}")
                            # 最终备选方案：截图保存
                            backup_path = f"{download_path}_final_backup.png"
                            await img_element.screenshot(path=backup_path)
                            print(f"💾 已保存最终备份: {backup_path}")
                    else:
                        print("❌ 未找到下载按钮")

                        # 分析悬停后的DOM结构
                        print("🔍 分析悬停后的DOM结构...")
                        hover_analysis = await page.evaluate("""() => {
                            // 查找所有可能的下载相关元素
                            const downloadElements = [];

                            // 查找包含下载相关属性的元素
                            const selectors = [
                                '[data-testid*="download"]',
                                '[class*="download"]',
                                '[class*="hover"]',
                                'svg[viewBox="0 0 24 24"]',
                                '.semi-icon'
                            ];

                            selectors.forEach(selector => {
                                const elements = document.querySelectorAll(selector);
                                elements.forEach(el => {
                                    downloadElements.push({
                                        selector: selector,
                                        tagName: el.tagName,
                                        className: el.className,
                                        testId: el.getAttribute('data-testid'),
                                        textContent: el.textContent.trim().substring(0, 50),
                                        visible: el.offsetParent !== null
                                    });
                                });
                            });

                            return downloadElements;
                        }""")

                        print(f"DOM分析结果: {hover_analysis}")

                        # 截图保存当前状态
                        hover_screenshot = f"{OUTPUT_DIR}/hover_state_{i+1}_{timestamp}.png"
                        await page.screenshot(path=hover_screenshot)
                        print(f"📸 已保存悬停状态截图: {hover_screenshot}")

                except Exception as e:
                    print(f"❌ 处理第 {i+1} 张图片时出错: {e}")

                # 移开鼠标，避免影响下一张图片
                await page.mouse.move(0, 0)
                await asyncio.sleep(2)  # 增加等待时间，确保状态清除

            print(f"\n🎉 测试完成！共测试了 {len(images_info)} 张图片")

        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            await page.screenshot(path=f"{OUTPUT_DIR}/error_{timestamp}.png")

        finally:
            # 等待用户查看结果
            print("⏳ 等待30秒让用户查看结果...")
            await asyncio.sleep(30)

            # 关闭浏览器
            await browser.close()

if __name__ == "__main__":
    asyncio.run(main())
