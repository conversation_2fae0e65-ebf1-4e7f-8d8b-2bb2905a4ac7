#!/usr/bin/env python3
"""
豆包图片下载功能专项测试脚本
基于提供的HTML结构信息进行精确测试
"""

import asyncio
import os
import json
from datetime import datetime
from playwright.async_api import async_playwright

# 配置
OUTPUT_DIR = "test_images"
AUTH_STATE_FILE = "doubao_auth_state.json"
TEST_URL = "https://www.doubao.com/chat/7342260912932610"

async def main():
    print("🧪 豆包图片下载功能专项测试...")

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    async with async_playwright() as p:
        # 启动浏览器
        print("🚀 启动Chrome浏览器...")
        browser = await p.chromium.launch(
            channel='chrome',  # 使用系统安装的Chrome
            headless=False,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process'
            ]
        )

        # 创建上下文
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )

        # 加载登录状态
        if os.path.exists(AUTH_STATE_FILE):
            print(f"📂 加载保存的登录状态: {AUTH_STATE_FILE}")
            with open(AUTH_STATE_FILE, 'r') as f:
                auth_state = json.load(f)

            # 设置cookies
            if 'cookies' in auth_state:
                await context.add_cookies(auth_state['cookies'])
                print(f"✅ 成功加载 {len(auth_state['cookies'])} 个cookies")

        # 创建页面
        page = await context.new_page()

        # 设置下载路径
        await page.set_extra_http_headers({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        })

        try:
            # 访问测试页面
            print(f"🌐 访问测试页面: {TEST_URL}")
            response = await page.goto(TEST_URL, wait_until='networkidle', timeout=30000)
            print(f"✅ 页面加载成功，状态码: {response.status}")

            # 等待页面完全加载
            await asyncio.sleep(5)

            # 保存页面截图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            await page.screenshot(path=f"{OUTPUT_DIR}/test_page_{timestamp}.png")
            print(f"📸 已保存页面截图")

            # 查找所有图片
            print("🔍 查找页面中的所有图片...")
            images_info = await page.evaluate("""() => {
                const images = [];
                const processedSrcs = new Set(); // 避免重复

                // 优先查找img标签，因为它们更容易操作
                const imgs = document.querySelectorAll('img');
                imgs.forEach((img, index) => {
                    const src = img.src;
                    if (src && src.includes('image_skill') && !processedSrcs.has(src)) {
                        processedSrcs.add(src);
                        images.push({
                            type: 'img',
                            index: index,
                            src: src,
                            width: img.width || img.naturalWidth,
                            height: img.height || img.naturalHeight,
                            element: img
                        });
                    }
                });

                // 如果没有找到img标签，再查找source标签
                if (images.length === 0) {
                    const sources = document.querySelectorAll('source[type="image/avif"]');
                    sources.forEach((source, index) => {
                        const srcset = source.getAttribute('srcset');
                        if (srcset && srcset.includes('image_skill')) {
                            const src = srcset.split(' ')[0]; // 取第一个URL
                            if (!processedSrcs.has(src)) {
                                processedSrcs.add(src);
                                images.push({
                                    type: 'source_avif',
                                    index: index,
                                    srcset: srcset,
                                    src: src,
                                    element: source
                                });
                            }
                        }
                    });
                }

                console.log(`找到 ${images.length} 张唯一图片`);
                return images;
            }""")

            print(f"📊 找到 {len(images_info)} 张图片")

            # 为每张图片测试下载功能
            for i, image_info in enumerate(images_info):
                print(f"\n🖼️ 测试第 {i+1} 张图片下载...")
                print(f"   类型: {image_info['type']}")
                print(f"   源: {image_info['src'][:100]}...")

                try:
                    # 根据图片类型找到对应的元素
                    if image_info['type'] == 'source_avif':
                        # 对于source标签，需要找到其父级picture或相关的img
                        target_element = await page.evaluate("""(index) => {
                            const sources = document.querySelectorAll('source[type="image/avif"]');
                            const source = sources[index];
                            if (!source) return null;

                            // 查找父级picture元素中的img
                            const picture = source.closest('picture');
                            if (picture) {
                                const img = picture.querySelector('img');
                                return img;
                            }

                            // 或者查找紧邻的img元素
                            let sibling = source.nextElementSibling;
                            while (sibling) {
                                if (sibling.tagName === 'IMG') {
                                    return sibling;
                                }
                                sibling = sibling.nextElementSibling;
                            }

                            return null;
                        }""", image_info['index'])

                        if not target_element:
                            print("❌ 未找到对应的img元素")
                            continue

                    else:
                        # 直接使用img元素
                        target_element = f"img[src='{image_info['src']}']"

                    # 找到图片元素
                    if image_info['type'] == 'source_avif':
                        # 对于source，我们需要通过JavaScript找到元素
                        img_element = await page.evaluate("""(index) => {
                            const sources = document.querySelectorAll('source[type="image/avif"]');
                            const source = sources[index];
                            if (!source) return null;

                            const picture = source.closest('picture');
                            if (picture) {
                                return picture.querySelector('img');
                            }
                            return null;
                        }""", image_info['index'])

                        if img_element:
                            # 通过坐标定位元素
                            img_selector = f"img[src*='image_skill']:nth-of-type({i+1})"
                            img_element = await page.query_selector(img_selector)
                    else:
                        img_element = await page.query_selector(f"img[src='{image_info['src']}']")

                    if not img_element:
                        print("❌ 未找到图片元素")
                        continue

                    print("✅ 找到图片元素，开始悬停...")

                    # 鼠标悬停到图片上
                    await img_element.hover()
                    await asyncio.sleep(2)  # 等待悬停效果出现

                    # 查找下载按钮
                    print("🔍 查找下载按钮...")
                    download_button = await page.query_selector('[data-testid="edit_image_hover_tag_download_btn"]')

                    if download_button:
                        print("✅ 找到下载按钮！")

                        # 设置下载监听
                        download_path = os.path.join(os.path.abspath(OUTPUT_DIR), f"downloaded_image_{i+1}_{timestamp}")

                        try:
                            # 方法1：尝试监听下载事件
                            print("🔄 尝试方法1：监听下载事件...")
                            download_success = False

                            try:
                                async with page.expect_download(timeout=5000) as download_info:
                                    await download_button.click()
                                    print("✅ 已点击下载按钮")

                                    download = await download_info.value
                                    print(f"📥 下载开始: {download.suggested_filename}")

                                    final_path = f"{download_path}_{download.suggested_filename}"
                                    await download.save_as(final_path)
                                    print(f"🎉 下载成功: {final_path}")
                                    download_success = True
                            except Exception as e:
                                print(f"⚠️ 方法1失败: {e}")

                            # 方法2：如果下载事件失败，尝试直接获取图片URL并下载
                            if not download_success:
                                print("🔄 尝试方法2：直接下载图片...")
                                try:
                                    # 点击下载按钮（可能触发其他行为）
                                    await download_button.click()
                                    await asyncio.sleep(2)

                                    # 获取原始图片URL
                                    original_url = await page.evaluate("""(src) => {
                                        // 尝试从src中提取原始URL
                                        if (src.includes('~tplv-')) {
                                            // 移除缩略图参数，获取原图
                                            return src.split('~tplv-')[0] + '_origin';
                                        }
                                        return src;
                                    }""", image_info['src'])

                                    print(f"🔗 原始图片URL: {original_url}")

                                    # 使用页面直接下载
                                    download_result = await page.evaluate("""async (url, filename) => {
                                        try {
                                            const response = await fetch(url);
                                            const blob = await response.blob();

                                            // 创建下载链接
                                            const a = document.createElement('a');
                                            const objectUrl = URL.createObjectURL(blob);
                                            a.href = objectUrl;
                                            a.download = filename;
                                            document.body.appendChild(a);
                                            a.click();
                                            document.body.removeChild(a);
                                            URL.revokeObjectURL(objectUrl);

                                            return { success: true, size: blob.size };
                                        } catch (error) {
                                            return { success: false, error: error.message };
                                        }
                                    }""", original_url, f"doubao_image_{i+1}.jpg")

                                    if download_result.get('success'):
                                        print(f"🎉 方法2下载成功: {download_result}")
                                        download_success = True
                                    else:
                                        print(f"❌ 方法2失败: {download_result}")

                                except Exception as e:
                                    print(f"❌ 方法2出错: {e}")

                            # 方法3：如果都失败，保存截图
                            if not download_success:
                                print("🔄 方法3：保存截图备份...")
                                backup_path = f"{download_path}_screenshot.png"
                                await img_element.screenshot(path=backup_path)
                                print(f"💾 已保存截图备份: {backup_path}")

                        except Exception as e:
                            print(f"❌ 下载过程出错: {e}")
                            # 最终备选方案：截图保存
                            backup_path = f"{download_path}_final_backup.png"
                            await img_element.screenshot(path=backup_path)
                            print(f"💾 已保存最终备份: {backup_path}")
                    else:
                        print("❌ 未找到下载按钮")

                        # 分析悬停后的DOM结构
                        print("🔍 分析悬停后的DOM结构...")
                        hover_analysis = await page.evaluate("""() => {
                            // 查找所有可能的下载相关元素
                            const downloadElements = [];

                            // 查找包含下载相关属性的元素
                            const selectors = [
                                '[data-testid*="download"]',
                                '[class*="download"]',
                                '[class*="hover"]',
                                'svg[viewBox="0 0 24 24"]',
                                '.semi-icon'
                            ];

                            selectors.forEach(selector => {
                                const elements = document.querySelectorAll(selector);
                                elements.forEach(el => {
                                    downloadElements.push({
                                        selector: selector,
                                        tagName: el.tagName,
                                        className: el.className,
                                        testId: el.getAttribute('data-testid'),
                                        textContent: el.textContent.trim().substring(0, 50),
                                        visible: el.offsetParent !== null
                                    });
                                });
                            });

                            return downloadElements;
                        }""")

                        print(f"DOM分析结果: {hover_analysis}")

                        # 截图保存当前状态
                        hover_screenshot = f"{OUTPUT_DIR}/hover_state_{i+1}_{timestamp}.png"
                        await page.screenshot(path=hover_screenshot)
                        print(f"📸 已保存悬停状态截图: {hover_screenshot}")

                except Exception as e:
                    print(f"❌ 处理第 {i+1} 张图片时出错: {e}")

                # 移开鼠标，避免影响下一张图片
                await page.mouse.move(0, 0)
                await asyncio.sleep(1)

            print(f"\n🎉 测试完成！共测试了 {len(images_info)} 张图片")

        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            await page.screenshot(path=f"{OUTPUT_DIR}/error_{timestamp}.png")

        finally:
            # 等待用户查看结果
            print("⏳ 等待30秒让用户查看结果...")
            await asyncio.sleep(30)

            # 关闭浏览器
            await browser.close()

if __name__ == "__main__":
    asyncio.run(main())
